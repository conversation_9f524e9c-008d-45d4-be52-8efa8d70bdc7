#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include "DeviceConfig.h"
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "TouchSensorManager.h"
#include "OTAManager.h"
// Platform-specific includes are now handled in DeviceConfig.h

// Include device-specific templates
#ifdef IS_ESP32
#include "WebTemplates_ESP32.h"
#ifdef COMPILE_FULL_COLOR_RGB
#include "FullColorRGBManager.h"
#endif
#ifdef COMPILE_COLOR_CYCLE
#include "ColorCycleManager.h"
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
#include "SHT30TemperatureSensor.h"
#endif
#else
#include "WebTemplates_ESP8266.h"
#endif

class WebServerManager
{
private:
    WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    TouchSensorManager *_touchManager;
    OTAManager *_otaManager;
    bool _serverRunning;
    int _port;

    // SSE connection management
    WiFiClient _dashboardSSEClient;
    WiFiClient _switchesSSEClient;
    WiFiClient _temperatureSSEClient;
    bool _dashboardSSEActive;
    bool _switchesSSEActive;
    bool _temperatureSSEActive;
    unsigned long _lastDashboardUpdate;
    unsigned long _lastSwitchesUpdate;
    unsigned long _lastTemperatureUpdate;
    bool _stateChanged;
    unsigned long _lastStateChange;

    // ESP32-specific managers
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
    FullColorRGBManager *_fullColorRGBManager;
#endif
#ifdef COMPILE_COLOR_CYCLE
    ColorCycleManager *_colorCycleManager;
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
    SHT30TemperatureSensor *_temperatureSensor;
#endif
#endif

    // State change notification system (declared above with SSE variables)

    // Page throttling variables
    unsigned long _lastPageRequest = 0;
    unsigned long _lastScanRequest = 0;
    const unsigned long PAGE_THROTTLE_MS = 1000; // 1 second between page requests
    const unsigned long SCAN_THROTTLE_MS = 5000; // 5 seconds between scans
    String _currentRequestingIP = "";

    // Base HTML templates stored in PROGMEM for memory efficiency
    static const char baseHeader[] PROGMEM;
    static const char baseFooter[] PROGMEM;

    // Page-specific headers and footers
    static const char dashboardHeader[] PROGMEM;
    static const char switchesHeader[] PROGMEM;
    static const char wifiHeader[] PROGMEM;
    static const char mqttHeader[] PROGMEM;
    static const char systemUpdateHeader[] PROGMEM;

    // Page-specific templates in PROGMEM
    static const char dashboardTemplate[] PROGMEM;
    static const char switchesTemplate[] PROGMEM;
    static const char wifiTemplate[] PROGMEM;
    static const char wifiConnectTemplate[] PROGMEM;
    static const char wifiHiddenTemplate[] PROGMEM;
    static const char apControlTemplate[] PROGMEM;
    static const char deviceTemplate[] PROGMEM;
    static const char mqttTemplate[] PROGMEM;
    static const char systemUpdateTemplate[] PROGMEM;
#ifdef COMPILE_TEMPERATURE_SENSOR
    static const char temperatureTemplate[] PROGMEM;
    static const char temperatureHeader[] PROGMEM;
#endif
    static const char notFoundTemplate[] PROGMEM;

public:
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, TouchSensorManager *touchManager = nullptr, OTAManager *otaManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _touchManager(touchManager), _otaManager(otaManager), _serverRunning(false), _port(port), _stateChanged(false), _lastStateChange(0),
          _dashboardSSEActive(false), _switchesSSEActive(false), _temperatureSSEActive(false), _lastDashboardUpdate(0), _lastSwitchesUpdate(0), _lastTemperatureUpdate(0)
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
          ,
          _fullColorRGBManager(nullptr)
#endif
#ifdef COMPILE_COLOR_CYCLE
          ,
          _colorCycleManager(nullptr)
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
          ,
          _temperatureSensor(nullptr)
#endif
#endif
    {
    }

    // Public methods
    void begin();
    void handleClient();
    bool isRunning();
    void notifyStateChange();
    void stop();

    // ESP32 manager setters
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
    void setFullColorRGBManager(FullColorRGBManager *manager) { _fullColorRGBManager = manager; }
#endif
#ifdef COMPILE_COLOR_CYCLE
    void setColorCycleManager(ColorCycleManager *manager) { _colorCycleManager = manager; }
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
    void setTemperatureSensor(SHT30TemperatureSensor *sensor) { _temperatureSensor = sensor; }
#endif
#endif

private:
    // Helper function to convert RGB boolean values to color name
    String getColorNameFromRGB(bool r, bool g, bool b)
    {
        if (!r && !g && !b)
            return "off";
        if (r && !g && !b)
            return "red";
        if (!r && g && !b)
            return "green";
        if (!r && !g && b)
            return "blue";
        if (r && g && !b)
            return "yellow";
        if (!r && g && b)
            return "cyan";
        if (r && !g && b)
            return "magenta";
        if (r && g && b)
            return "white";
        return "off";
    }

    // Send complete HTML page using PROGMEM templates
    void sendHtmlPage(const String &content);
    void sendProgmemPage(const char *progmemTemplate, const char *pageHeader = nullptr);

    // Memory management functions
    void checkMemoryUsage(const String &context = "");
    bool hasEnoughMemory(uint32_t requiredBytes = 3072);
    bool isRequestThrottled(bool isScan = false);

    // Page handlers
    void handleRoot();
    void handleDevice();
    void handleSwitches();
    void handleWiFi();
    void handleWiFiConnectPage();
    void handleWiFiHiddenPage();
    void handleAPControl();
    void handleMqtt();
    void handleSystemUpdate();
#ifdef COMPILE_TEMPERATURE_SENSOR
    void handleTemperature();
#endif

    // API handlers
    void handleSwitch();
    void handleRGBOff();
    void handleRGBOn();
    void handleWiFiScan();
    void handleWiFiConnect();
    void handleAPStatus();
    void handleAPConfig();
    void handleStatusAPI();
    void handleSwitchesAPI();
    void handleSystemUpdateAPI();
    void handleUpdateCheck();
    void handleUpdateToggle();
    void handleInstallUpdate();

    // ESP32 RGB API handlers
#ifdef IS_ESP32
    void handleRGBSet();
    void handleRGBPreset();
    void handleRGBBrightness();
    void handleColorCycleMode();
    void handleColorCycleSpeed();
    void handleColorCycleSaturation();
    void handleColorCycleMask();
#endif

    void handleDashboardSSE();
    void handleSwitchesSSE();
#ifdef COMPILE_TEMPERATURE_SENSOR
    void handleTemperatureSSE();
#endif
    void handle404();

    // SSE connection management methods
    void updateSSEConnections();
    void sendDashboardSSEUpdate();
    void sendSwitchesSSEUpdate();
#ifdef COMPILE_TEMPERATURE_SENSOR
    void sendTemperatureSSEUpdate();
#endif
    void closeSSEConnection(WiFiClient &client, bool &activeFlag, const String &connectionName);
};

// PROGMEM HTML template definitions
// Base header with essential CSS only
const char WebServerManager::baseHeader[] PROGMEM = R"=====(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Control Panel</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #1a1a1a; color: #e0e0e0; }
        .navbar { background-color: #2d2d2d; padding: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); border-bottom: 2px solid #0066cc; }
        .nav-container { max-width: 1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between; }
        .nav-brand { padding: 15px 20px; font-size: 1.2em; font-weight: bold; color: #ccc; text-decoration: none; }
        .nav-toggle { display: none; background: none; border: none; color: #e0e0e0; font-size: 1.8em; padding: 15px 20px; cursor: pointer; z-index: 1001; position: relative; }
        .nav-menu { display: flex; list-style: none; margin: 0; padding: 0; flex: 1; }
        .nav-item { margin: 0; }
        .nav-link { display: block; padding: 15px 20px; color: #e0e0e0; text-decoration: none; transition: background-color 0.3s, color 0.3s; border-radius: 0; }
        .nav-link:hover { background-color: #0066cc; color: #fff; }
        .nav-link.active { background-color: #0066cc; color: white; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .content-section { background-color: #242424; padding: 30px; border-radius: 15px; margin-bottom: 20px; border-top: 3px solid #0066cc; }
        .info-card { background-color: #2d2d2d; border: 1px solid #444; border-radius: 8px; padding: 20px; margin: 12px 0; border-left: 4px solid #0066cc; }
        h1 { color: #ccc; text-align: center; margin-bottom: 30px; font-size: 2em; }
        h2 { color: #bbb; border-bottom: 2px solid #0066cc; padding-bottom: 10px; margin-bottom: 20px; }
        h3 { color: #bbb; margin-bottom: 15px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 30px; }
        .info-item { background-color: #2d2d2d; padding: 12px 16px; border-radius: 12px; border-left: 4px solid #0066cc; display: flex; justify-content: space-between; align-items: center; }
        .info-label { font-weight: bold; color: #0066cc; font-size: 0.9em; flex-shrink: 0; margin-right: 10px; }
        .info-value { color: #e0e0e0; font-size: 1.1em; word-break: break-word; text-align: right; flex-grow: 1; }
        button, .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: bold; text-decoration: none; display: inline-block; text-align: center; transition: all 0.3s; }
        .btn-primary { background-color: #0066cc; color: white; }
        .btn-primary:hover { background-color: #0052a3; }
        .btn-secondary { background-color: #555; color: white; }
        .btn-secondary:hover { background-color: #666; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-success:hover { background-color: #218838; }
        .button-container { margin: 15px 0; }
        .loading-message { text-align: center; color: #888; padding: 20px; background-color: transparent; border: none; }
        .mobile-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); backdrop-filter: blur(5px); z-index: 999; }
        .mobile-overlay.active { display: block; }
        .mobile-menu { display: none; position: fixed; top: 0; right: -300px; width: 300px; height: 100%; background-color: #2d2d2d; z-index: 1000; transition: right 0.3s; padding-top: 60px; }
        .mobile-menu.active { right: 0; display: block; }
        .mobile-menu .nav-link { display: block; padding: 15px 20px; color: #e0e0e0; text-decoration: none; border-bottom: 1px solid #444; }
        .mobile-menu .nav-link:hover { background-color: #0066cc; }
        @media (max-width: 768px) { .nav-toggle { display: block; } .nav-menu { display: none; } }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">Hooshmand Rayan Hirmand</a>
            <button class="nav-toggle" onclick="toggleMobileMenu()">☰</button>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/" class="nav-link">Dashboard</a></li>
                <li class="nav-item"><a href="/wifi" class="nav-link">WiFi</a></li>
                <li class="nav-item"><a href="/ap-control" class="nav-link">AP Control</a></li>
                <li class="nav-item"><a href="/switches" class="nav-link">Switch Control</a></li>
#ifdef COMPILE_TEMPERATURE_SENSOR
                <li class="nav-item"><a href="/temperature" class="nav-link">Temperature</a></li>
#endif
                <li class="nav-item"><a href="/mqtt" class="nav-link">MQTT</a></li>
                <li class="nav-item"><a href="/system-update" class="nav-link">System Update</a></li>
            </ul>
        </div>
    </nav>
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileMenu()"></div>
    <div class="mobile-menu" id="mobileMenu">
        <a href="/" class="nav-link">Dashboard</a>
        <a href="/wifi" class="nav-link">WiFi</a>
        <a href="/ap-control" class="nav-link">AP Control</a>
        <a href="/switches" class="nav-link">Switch Control</a>
#ifdef COMPILE_TEMPERATURE_SENSOR
        <a href="/temperature" class="nav-link">Temperature</a>
#endif
        <a href="/mqtt" class="nav-link">MQTT</a>
        <a href="/system-update" class="nav-link">System Update</a>
    </div>
    <div class="container">
)=====";

// Dashboard-specific header (adds SSE functionality for real-time updates)
const char WebServerManager::dashboardHeader[] PROGMEM = R"=====(
<style>
#wifi-details .info-item { margin-bottom: 8px; }
#wifi-details .info-item:last-child { margin-bottom: 0; }
</style>
<script>
let dashboardEventSource = null;
function startDashboardSSE() {
if (dashboardEventSource) dashboardEventSource.close();
dashboardEventSource = new EventSource('/api/dashboard-updates');
dashboardEventSource.onopen = () => console.log('Dashboard SSE connected');
dashboardEventSource.addEventListener('connected', () => console.log('Dashboard SSE ready'));
dashboardEventSource.addEventListener('update', (e) => {
const d = JSON.parse(e.data);
document.getElementById('free-memory').textContent = (d.freeHeap || 'Unknown') + ' bytes';
document.getElementById('uptime').textContent = d.uptime || 'Unknown';
});
dashboardEventSource.onerror = () => {
console.log('Dashboard SSE connection closed, will reconnect in 2 seconds');
setTimeout(startDashboardSSE, 2000);
};
}
function stopDashboardSSE() { if (dashboardEventSource) { dashboardEventSource.close(); dashboardEventSource = null; } }
window.addEventListener('beforeunload', () => stopDashboardSSE());
</script>
)=====";

// Switches-specific header (adds switch controls, color picker styles, and real-time SSE updates)
const char WebServerManager::switchesHeader[] PROGMEM = R"=====(
<script>
let switchesEventSource = null;



function updateSwitchesDisplay(data) {
    if (!data.switchCount || !data.switchState) return;

    for (let i = 0; i < data.switchCount; i++) {
        const state = data.switchState[i];
        const switchCard = document.querySelector('.switch-card:nth-child(' + (i+1) + ')');
        if (switchCard) {
            // Update switch state display
            const stateElement = switchCard.querySelector('.switch-state');
            if (stateElement) {
                stateElement.className = 'switch-state ' + (state ? 'on' : 'off');
                stateElement.textContent = state ? 'ON' : 'OFF';
            }

            // Update toggle switch
            const toggleSwitch = switchCard.querySelector('.toggle-switch');
            if (toggleSwitch) {
                toggleSwitch.className = 'toggle-switch ' + (state ? 'on' : '');
            }

            // Update color dropdowns if color data is available
            if (data.rgbOffColors && data.rgbOnColors) {
                const offDropdown = switchCard.querySelector('.color-dropdown.off');
                if (offDropdown) {
                    const offColor = data.rgbOffColors[i];
                    offDropdown.value = offColor;
                    offDropdown.className = offDropdown.className.replace(/\b(red|green|blue|yellow|cyan|magenta|white|off)\b/g, '');
                    offDropdown.className += ' ' + offColor;
                }

                const onDropdown = switchCard.querySelector('.color-dropdown.on');
                if (onDropdown) {
                    const onColor = data.rgbOnColors[i];
                    onDropdown.value = onColor;
                    onDropdown.className = onDropdown.className.replace(/\b(red|green|blue|yellow|cyan|magenta|white|off)\b/g, '');
                    onDropdown.className += ' ' + onColor;
                }
            }
        }
    }
}

function startSwitchesSSE() {
    if (switchesEventSource) {
        switchesEventSource.close();
        switchesEventSource = null;
    }

    switchesEventSource = new EventSource('/api/switches/events');

    switchesEventSource.onopen = function() {
        console.log('Switches SSE connected');
    };

    switchesEventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            updateSwitchesDisplay(data);
        } catch (e) {
            console.error('Error parsing SSE data:', e);
        }
    };

    switchesEventSource.onerror = function(event) {
        console.log('Switches SSE connection error, retrying in 3 seconds...');
        setTimeout(() => {
            if (switchesEventSource) {
                switchesEventSource.close();
                switchesEventSource = null;
            }
            startSwitchesSSE();
        }, 3000);
    };
}

function stopSwitchesSSE() {
    if (switchesEventSource) {
        switchesEventSource.close();
        switchesEventSource = null;
    }
}

function setRGBColor(switchIndex, state, color) {
    var r = 0, g = 0, b = 0;
    switch(color) {
        case 'red': r = 1; break; case 'green': g = 1; break; case 'blue': b = 1; break;
        case 'yellow': r = 1; g = 1; break; case 'cyan': g = 1; b = 1; break;
        case 'magenta': r = 1; b = 1; break; case 'white': r = 1; g = 1; b = 1; break;
        case 'off': break;
    }
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/rgb/' + state, true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);
    xhr.onload = function() {
        if (xhr.status === 200) {
            console.log('RGB color set for switch', switchIndex, 'state', state, 'to', color);
        }
    };
    var dropdown = event.target;
    if (dropdown) {
        dropdown.className = dropdown.className.replace(/\b(red|green|blue|yellow|cyan|magenta|white|off)\b/g, '');
        dropdown.className += ' ' + color;
    }
    var preview = document.getElementById('preview_' + state + '_' + switchIndex);
    if (preview) preview.style.backgroundColor = 'rgb(' + (r*255) + ',' + (g*255) + ',' + (b*255) + ')';
}

window.addEventListener('beforeunload', stopPeriodicUpdates);
</script>
<style>
.switch-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
.switch-card { background-color: #2d2d2d; padding: 20px; border-radius: 12px; border-left: 4px solid #0066cc; }
.switch-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.switch-title { font-size: 1.2em; font-weight: bold; color: #bbb; }
.switch-state { font-weight: bold; padding: 6px 12px; border-radius: 20px; font-size: 0.9em; }
.switch-state.on { background-color: #28a745; color: white; }
.switch-state.off { background-color: #dc3545; color: white; }
.switch-status { padding: 5px 15px; border-radius: 20px; font-weight: bold; text-transform: uppercase; }
.switch-status.on { background-color: #28a745; color: white; }
.switch-status.off { background-color: #dc3545; color: white; }
.switch-container { background-color: #2d2d2d; margin-bottom: 20px; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50; }
.toggle-container { display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; }
.toggle-container span { font-weight: bold; color: #0066cc; font-size: 0.9em; flex-shrink: 0; }
.toggle-switch { position: relative; width: 60px; height: 30px; background-color: #555; border-radius: 15px; cursor: pointer; transition: background-color 0.3s; }
.toggle-switch.on { background-color: #0066cc; }
.toggle-switch::after { content: ''; position: absolute; width: 26px; height: 26px; border-radius: 50%; background-color: white; top: 2px; left: 2px; transition: transform 0.3s; }
.toggle-switch.on::after { transform: translateX(30px); }


/* ESP8266 Basic RGB Controls - Dark Theme */
.color-controls {
    margin-top: 15px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.color-group {
    flex: 1;
    min-width: 120px;
    background-color: #3a3a3a;
    padding: 12px 16px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.color-group h4 {
    margin: 0;
    color: #bbb;
    font-size: 0.9em;
    font-weight: bold;
    flex-shrink: 0;
}

.color-selector {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-dropdown {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #666;
    border-radius: 8px;
    background-color: #2a2a2a;
    color: #e0e0e0;
    font-size: 14px;
    text-align: center;
    max-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-dropdown:focus {
    outline: none;
    border-color: #0066cc;
}

/* Color-specific styling for dropdowns - Dark Theme */
.color-dropdown.red { background-color: #cc3333; color: white; border-color: #cc3333; }
.color-dropdown.green { background-color: #33cc33; color: white; border-color: #33cc33; }
.color-dropdown.blue { background-color: #3366cc; color: white; border-color: #3366cc; }
.color-dropdown.yellow { background-color: #cccc33; color: black; border-color: #cccc33; }
.color-dropdown.cyan { background-color: #33cccc; color: black; border-color: #33cccc; }
.color-dropdown.magenta { background-color: #cc33cc; color: white; border-color: #cc33cc; }
.color-dropdown.white { background-color: #ffffff; color: black; border-color: #ffffff; }
.color-dropdown.off { background-color: #1a1a1a; color: #888; border-color: #666; }

.scenario-key {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    color: white;
}

/* Device-specific CSS additions */
#ifdef IS_ESP32
/* ESP32 Advanced RGB Controls */
.global-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.global-controls h2 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.control-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255,255,255,0.3);
    outline: none;
    -webkit-appearance: none;
}

.switch-card.esp32 {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.rgb-controls {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
}

.color-picker-group input[type="color"] {
    width: 50px;
    height: 35px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.rgb-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
}

.rgb-input {
    display: flex;
    flex-direction: column;
    gap: 3px;
    flex: 1;
}

.preset-colors {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.preset-colors button {
    padding: 6px 12px;
    border: none;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}
#endif

@media (max-width: 768px) {
    .color-controls {
        flex-direction: column;
        gap: 10px;
    }

    .color-group {
        min-width: auto;
    }
}

</style>
)=====";

// WiFi-specific header (adds WiFi-specific styles)
const char WebServerManager::wifiHeader[] PROGMEM = R"=====(
<style>
.wifi-page-container { }
.wifi-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.wifi-title { margin: 0; color: #ccc; }
.scan-btn { padding: 10px 20px; background-color: #0066cc; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
.scan-btn:hover { background-color: #0052a3; }
.scan-btn:disabled { background-color: #555; cursor: not-allowed; }
.separator-line { height: 2px; background-color: #0066cc; margin: 20px 0; border-radius: 1px; }
.network-list { max-height: 400px; overflow-y: auto; border: none; border-radius: 0; background-color: #1a1a1a; margin-bottom: 20px; padding: 0; }
.network-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin-bottom: 12px;
    background-color: #3a3a3a;
    border-radius: 12px;
    border-left: 4px solid #0066cc;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}
.network-item:hover { background-color: #4a4a4a; transform: translateY(-2px); }
.network-item:last-child { margin-bottom: 0; }
.network-info { flex-grow: 1; }
.network-name { font-weight: bold; color: #ffffff; margin-bottom: 3px; font-size: 16px; }
.network-security { font-size: 13px; color: #999; }
.signal-strength { margin-left: 15px; }
.signal-badge {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    text-transform: uppercase;
}
.signal-excellent { background-color: #8BC34A; }
.signal-good { background-color: #4CAF50; }
.signal-fair { background-color: #FFC107; }
.signal-weak { background-color: #F44336; }
.form-group { margin-bottom: 20px; }
.form-group label { display: block; margin-bottom: 8px; color: #bbb; font-weight: bold; }
.form-group input { width: 100%; padding: 12px; border: 1px solid #666; border-radius: 8px; background-color: #2a2a2a; color: #e0e0e0; font-size: 14px; box-sizing: border-box; }
.form-group input:focus { border-color: #0066cc; outline: none; }
.password-container { position: relative; }
.password-toggle { position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #0066cc; cursor: pointer; font-size: 14px; }
.password-toggle:hover { color: #0052a3; }
.form-buttons { margin-top: 25px; }
.button-row { display: flex; gap: 15px; }
.connect-button { flex: 1; padding: 12px; background-color: #0066cc; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
.connect-button:hover { background-color: #0052a3; }
.connect-button:disabled { background-color: #555; cursor: not-allowed; }
.cancel-button { flex: 1; padding: 12px; background-color: #666; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
.cancel-button:hover { background-color: #777; }
.hidden-network-button { padding: 12px 24px; background-color: #666; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; }
.hidden-network-button:hover { background-color: #777; }
#networkList .loading-message { text-align: center; color: #999; padding: 20px; font-style: italic; background-color: #242424 !important; border-radius: 0; border: none; }
.loading-spinner { display: inline-block; width: 16px; height: 16px; border: 2px solid #ffffff; border-radius: 50%; border-top-color: transparent; animation: spin 1s ease-in-out infinite; }
@keyframes spin { to { transform: rotate(360deg); } }
</style>
)=====";

// MQTT-specific header (minimal)
const char WebServerManager::mqttHeader[] PROGMEM = R"=====(
)=====";

// System Update-specific header (adds SSE functionality for real-time OTA progress and custom styling)
const char WebServerManager::systemUpdateHeader[] PROGMEM = R"=====(
<style>
/* Reduce padding for system update page containers */
.system-update .info-card { padding: 8px 20px; }
.system-update .content-section { padding: 20px; }

/* Toggle switch styling */
.toggle-switch-container { display: flex; align-items: center; gap: 10px; }
.toggle-switch { position: relative; display: inline-block; width: 50px; height: 24px; }
.toggle-switch input { opacity: 0; width: 0; height: 0; }
.toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #444; transition: .4s; border-radius: 24px; }
.toggle-slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: #ccc; transition: .4s; border-radius: 50%; }
input:checked + .toggle-slider { background-color: #0066cc; }
input:checked + .toggle-slider:before { transform: translateX(26px); }
.toggle-label { color: #ccc; font-size: 14px; }
</style>
<script>
function showUpdateMessage() {
    const s = document.getElementById('updateStatus');
    const det = document.getElementById('updateDetails');
    const prog = document.getElementById('updateProgress');

    s.textContent = 'Update in progress...';
    det.style.display = 'none';
    prog.style.display = 'block';

    // Hide progress bar and show simple message
    const bar = document.getElementById('progressBar');
    const txt = document.getElementById('progressText');
    if (bar) bar.style.display = 'none';
    if (txt) {
        txt.innerHTML = '<div style="text-align: center; padding: 20px; font-size: 16px; line-height: 1.6;">' +
            '<div style="margin-bottom: 15px; font-weight: bold; color: #0066cc;">⚠️ Update in Progress</div>' +
            '<div style="margin-bottom: 10px;">Please do not turn off the device during the update.</div>' +
            '<div style="margin-bottom: 10px;">The device will automatically restart when the update is complete.</div>' +
            '<div style="color: #888; font-size: 14px;">This process may take 1-2 minutes.</div>' +
        '</div>';
    }

    // Start checking for device restart
    startReconnectionCheck();
}

function startReconnectionCheck() {
    const reconnectInterval = setInterval(() => {
        fetch('/api/status', {
            method: 'GET',
            cache: 'no-cache',
            signal: AbortSignal.timeout(3000)
        })
        .then(r => {
            if (r.ok) {
                clearInterval(reconnectInterval);
                // Show completion message briefly before reload
                const txt = document.getElementById('progressText');
                if (txt) {
                    txt.innerHTML = '<div style="text-align: center; padding: 20px; font-size: 16px; color: #28a745;">' +
                        '<div style="margin-bottom: 10px; font-weight: bold;">✅ Update Complete!</div>' +
                        '<div>Refreshing page...</div>' +
                    '</div>';
                }
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        })
        .catch(e => {
            // Device still restarting, continue checking
        });
    }, 3000); // Check every 3 seconds

    // Stop checking after 3 minutes and show manual refresh message
    setTimeout(() => {
        clearInterval(reconnectInterval);
        const txt = document.getElementById('progressText');
        if (txt) {
            txt.innerHTML = '<div style="text-align: center; padding: 20px; font-size: 16px;">' +
                '<div style="margin-bottom: 10px; color: #ffc107;">⏱️ Update may be complete</div>' +
                '<div style="margin-bottom: 15px;">Please manually refresh the page to check if the update finished.</div>' +
                '<button onclick="window.location.reload()" style="padding: 10px 20px; background: #0066cc; color: white; border: none; border-radius: 5px; cursor: pointer;">Refresh Page</button>' +
            '</div>';
        }
    }, 180000); // 3 minutes
}
window.addEventListener('beforeunload', () => stopProgressSSE());
</script>
)=====";

// Base footer with navigation JavaScript
const char WebServerManager::baseFooter[] PROGMEM = R"=====(
    </div>
    <script>
        function setActiveNav(path) {
            var links = document.querySelectorAll('.nav-link');
            links.forEach(function(link) {
                link.classList.remove('active');
                if (link.getAttribute('href') === path) link.classList.add('active');
            });
        }
        function toggleMobileMenu() {
            var mobileMenu = document.getElementById('mobileMenu');
            var mobileOverlay = document.getElementById('mobileOverlay');
            mobileMenu.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
        }
        function closeMobileMenu() {
            var mobileMenu = document.getElementById('mobileMenu');
            var mobileOverlay = document.getElementById('mobileOverlay');
            mobileMenu.classList.remove('active');
            mobileOverlay.classList.remove('active');
        }
        document.addEventListener('DOMContentLoaded', function() {
            var mobileNavLinks = document.querySelectorAll('.mobile-menu .nav-link');
            mobileNavLinks.forEach(function(link) {
                link.addEventListener('click', function() { closeMobileMenu(); });
            });
        });
        var currentPath = window.location.pathname;
        setActiveNav(currentPath);
    </script>
</body>
</html>
)=====";

// Dashboard page template
const char WebServerManager::dashboardTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Dashboard</h1>
<h2>WiFi Connection</h2>
<div class='info-grid'>
<div class='info-item'>
<div class='info-label'>Status:</div>
<div class='info-value' id='wifi-status'>Checking...</div>
</div>
<div id='wifi-details'></div>
</div>
<h2>Device Information</h2>
<div class='info-grid'>
<div class='info-item'>
<div class='info-label'>Device ID:</div>
<div class='info-value' id='device-id'>Loading...</div>
</div>
<div class='info-item'>
<div class='info-label'>Device Name:</div>
<div class='info-value' id='device-name'>Loading...</div>
</div>
<div class='info-item'>
<div class='info-label'>Device Type:</div>
<div class='info-value' id='device-type'>Loading...</div>
</div>
<div class='info-item'>
<div class='info-label'>Switch Count:</div>
<div class='info-value' id='switch-count'>Loading...</div>
</div>
<div class='info-item'>
<div class='info-label'>Free Memory:</div>
<div class='info-value' id='free-memory'>Checking...</div>
</div>
<div class='info-item'>
<div class='info-label'>Uptime:</div>
<div class='info-value' id='uptime'>Checking...</div>
</div>
</div>
</div>
<script>
function loadDashboard(){
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
document.getElementById('wifi-status').textContent = d.wifiConnected ? 'Connected' : 'Disconnected';
if(d.wifiConnected){
document.getElementById('wifi-details').innerHTML =
'<div class="info-item"><div class="info-label">Network:</div><div class="info-value">' + (d.ssid || 'Unknown') + '</div></div>' +
'<div class="info-item"><div class="info-label">IP Address:</div><div class="info-value">' + (d.ipAddress || 'Unknown') + '</div></div>' +
'<div class="info-item"><div class="info-label">Signal:</div><div class="info-value">' + (d.rssi || 'Unknown') + ' dBm</div></div>';
document.getElementById('wifi-details').style.display = 'block';
} else {
document.getElementById('wifi-details').style.display = 'none';
}
if(d.deviceID) document.getElementById('device-id').textContent = d.deviceID;
if(d.deviceName) document.getElementById('device-name').textContent = d.deviceName;
if(d.deviceType) document.getElementById('device-type').textContent = d.deviceType;
if(d.switchCount !== undefined) document.getElementById('switch-count').textContent = d.switchCount;
document.getElementById('free-memory').textContent = (d.freeHeap || 'Unknown') + ' bytes';
document.getElementById('uptime').textContent = d.uptime || 'Unknown';
startDashboardSSE();
}).catch(e=>{
console.error('Dashboard load error:', e);
document.getElementById('wifi-status').textContent = 'Error loading data';
document.getElementById('free-memory').textContent = 'Error';
document.getElementById('uptime').textContent = 'Error';
});
}
window.addEventListener('load', function() { setActiveNav('/'); loadDashboard(); startDashboardSSE(); });
</script>
)=====";

// Switches page template (loads content dynamically via JavaScript)
const char WebServerManager::switchesTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Switch Control</h1>
<div class='switch-grid' id='switchGrid'>
<div class='loading-message'>Loading switches...</div>
</div>
</div>
<script>
function toggleSwitch(i){fetch('/switch',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'index='+i+'&state='+(document.querySelector('.switch-card:nth-child('+(i+1)+') .switch-state').classList.contains('on')?'0':'1')}).then(()=>{console.log('Switch toggle sent for index:', i);}).catch(e=>console.error('Toggle switch error:', e));}
function loadInitialSwitches(){
fetch('/api/status').then(r=>{
if(!r.ok) throw new Error('Status API failed: ' + r.status);
return r.json();
}).then(d=>{
let html = '';
for(let i = 0; i < d.switchCount; i++){
const state = d.switchState[i];
const offColor = d.rgbOffColors ? d.rgbOffColors[i] : 'off';
const onColor = d.rgbOnColors ? d.rgbOnColors[i] : 'off';
html += '<div class="switch-card"><div class="switch-header"><div class="switch-title">Switch ' + (i+1) + '</div>';
html += '<div class="switch-state ' + (state ? 'on' : 'off') + '">' + (state ? 'ON' : 'OFF') + '</div></div>';
html += '<div class="toggle-container"><span>Toggle Switch</span>';
html += '<div class="toggle-switch ' + (state ? 'on' : '') + '" onclick="toggleSwitch(' + i + ')"></div></div>';
html += '<div class="color-controls"><div class="color-group"><h4>OFF Color</h4><div class="color-selector">';
html += '<select class="color-dropdown off ' + offColor + '" onchange="setRGBColor(' + i + ', \'off\', this.value)">';
html += '<option value="off"' + (offColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (offColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (offColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (offColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (offColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (offColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (offColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (offColor === 'white' ? ' selected' : '') + '>White</option></select></div></div>';
html += '<div class="color-group"><h4>ON Color</h4><div class="color-selector">';
html += '<select class="color-dropdown on ' + onColor + '" onchange="setRGBColor(' + i + ', \'on\', this.value)">';
html += '<option value="off"' + (onColor === 'off' ? ' selected' : '') + '>Off</option>';
html += '<option value="red"' + (onColor === 'red' ? ' selected' : '') + '>Red</option>';
html += '<option value="green"' + (onColor === 'green' ? ' selected' : '') + '>Green</option>';
html += '<option value="blue"' + (onColor === 'blue' ? ' selected' : '') + '>Blue</option>';
html += '<option value="yellow"' + (onColor === 'yellow' ? ' selected' : '') + '>Yellow</option>';
html += '<option value="cyan"' + (onColor === 'cyan' ? ' selected' : '') + '>Cyan</option>';
html += '<option value="magenta"' + (onColor === 'magenta' ? ' selected' : '') + '>Magenta</option>';
html += '<option value="white"' + (onColor === 'white' ? ' selected' : '') + '>White</option></select></div></div></div></div>';
}
document.getElementById('switchGrid').innerHTML = html;
}).catch(e=>{
console.error('Switch loading error:', e);
document.getElementById('switchGrid').innerHTML = '<div class="loading-message">Error loading switches</div>';
});
}
window.addEventListener('load', function() { setActiveNav('/switches'); loadInitialSwitches(); startSwitchesSSE(); });
window.addEventListener('beforeunload', function() { stopSwitchesSSE(); });
</script>
)=====";

// WiFi page template
const char WebServerManager::wifiTemplate[] PROGMEM = R"=====(
<div class='content-section wifi-page-container'>
<div class='wifi-header'>
<h1 class='wifi-title'>WiFi</h1>
<button onclick='scanWiFi()' id='scanBtn' class='scan-btn'>Scan for Networks</button>
</div>
<div class='separator-line'></div>
<div id='currentNetworkSection' style='display: none; margin-bottom: 25px;'>
<h2 style='color: #ccc; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 3px solid #0066cc;'>Current Network:</h2>
<div id='currentNetwork' style='background-color: #242424; padding: 0;'></div>
</div>
<h2 style='color: #ccc; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 3px solid #0066cc;'>Available Networks:</h2>
<div id='networkList' class='network-list'>
<div class='loading-message'>Click "Scan for Networks" to see available WiFi networks</div>
</div>
<div class='button-container'>
<button onclick='showConnectToHidden()' class='btn btn-secondary' style='width:100%'>Connect to Hidden Network</button>
</div>
</div>
<script>
function scanWiFi() {
const btn = document.getElementById('scanBtn');
const list = document.getElementById('networkList');
btn.disabled = true;
btn.textContent = 'Scanning...';
list.innerHTML = '<div class="loading-message">Scanning for networks...</div>';
fetch('/api/wifi/scan').then(r => r.json()).then(d => {
if (d.error) {
list.innerHTML = '<div class="loading-message">Error: ' + d.error + '</div>';
} else if (d.networks && d.networks.length > 0) {
let html = '';
d.networks.forEach(n => {
const strength = n.rssi > -50 ? 'excellent' : n.rssi > -60 ? 'good' : n.rssi > -70 ? 'fair' : 'weak';
const strengthText = strength === 'excellent' ? 'Excellent' : strength === 'good' ? 'Good' : strength === 'fair' ? 'Fair' : 'Weak';
const security = n.encryption && n.encryption !== 'none' ? 'Secured' : 'Open';
const needsPassword = n.encryption && n.encryption !== 'none' ? '1' : '0';
html += '<div class="network-item" onclick="connectToNetwork(\'' + n.ssid + '\', ' + needsPassword + ')">';
html += '<div class="network-info">';
html += '<div class="network-name">' + n.ssid + '</div>';
html += '<div class="network-security">' + security + '</div>';
html += '</div>';
html += '<div class="signal-strength">';
html += '<span class="signal-badge signal-' + strength + '">' + strengthText + '</span>';
html += '</div>';
html += '</div>';
});
list.innerHTML = html;
} else {
list.innerHTML = '<div class="loading-message">No networks found</div>';
}
}).catch(e => {
list.innerHTML = '<div class="loading-message">Error scanning networks</div>';
}).finally(() => {
btn.disabled = false;
btn.textContent = 'Scan for Networks';
});
}
function connectToNetwork(ssid, needsPassword) {
window.location.href = '/wifi/connect?ssid=' + encodeURIComponent(ssid) + '&secure=' + needsPassword;
}
function showConnectToHidden() {
window.location.href = '/wifi/hidden';
}
function loadCurrentNetwork() {
fetch('/api/status').then(r => r.json()).then(d => {
const section = document.getElementById('currentNetworkSection');
const container = document.getElementById('currentNetwork');
if (d.wifiConnected && d.ssid) {
const strength = d.rssi > -50 ? 'excellent' : d.rssi > -60 ? 'good' : d.rssi > -70 ? 'fair' : 'weak';
const strengthText = strength === 'excellent' ? 'Excellent' : strength === 'good' ? 'Good' : strength === 'fair' ? 'Fair' : 'Weak';
container.innerHTML = '<div class="network-item"><div class="network-info"><div class="network-name">' + d.ssid + '</div><div class="network-security">Connected</div></div><div class="signal-strength"><span class="signal-badge signal-' + strength + '">' + strengthText + '</span></div></div>';
section.style.display = 'block';
} else {
section.style.display = 'none';
}
}).catch(e => console.error('Failed to load current network:', e));
}
window.addEventListener('load', function() { setActiveNav('/wifi'); loadCurrentNetwork(); });
</script>
)=====";

// WiFi connect page template
const char WebServerManager::wifiConnectTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1 class='wifi-title' id='networkTitle' style='margin-bottom: 20px;'>Connect to Network</h1>
<div class='separator-line'></div>
<div class='form-group' id='passwordGroup'>
<label for='networkPassword'>Password:</label>
<div class='password-container'>
<input type='password' id='networkPassword' placeholder='Enter network password'>
<button type='button' id='passwordToggle' class='password-toggle' onclick='togglePasswordVisibility()'>Show</button>
</div>
</div>
<div class='form-buttons' style='margin-top: 25px;'>
<div class='button-row'>
<button onclick='connectToNetwork()' class='connect-button' id='connectBtn'>Connect</button>
<button onclick='goBack()' class='cancel-button'>Cancel</button>
</div>
</div>
</div>
<script>
function getUrlParams(){const params=new URLSearchParams(window.location.search);return{ssid:params.get('ssid'),secure:params.get('secure')==='1'};}
function togglePasswordVisibility(){const i=document.getElementById('networkPassword'),b=document.getElementById('passwordToggle');if(i.type==='password'){i.type='text';b.textContent='Hide';}else{i.type='password';b.textContent='Show';}}
function connectToNetwork(){const params=getUrlParams();const password=document.getElementById('networkPassword').value;const btn=document.getElementById('connectBtn');btn.innerHTML='<span class="loading-spinner"></span> Connecting';btn.disabled=true;fetch('/api/wifi/connect',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'ssid='+encodeURIComponent(params.ssid)+'&password='+encodeURIComponent(password)}).then(r=>r.json()).then(d=>{if(d.success){window.location.href='/wifi';}else{btn.innerHTML='Failed: '+(d.message||'Connection failed');btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);}}).catch(()=>{btn.innerHTML='Connection failed';btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);});}
function goBack(){window.location.href='/wifi';}
window.addEventListener('load',function(){const params=getUrlParams();document.getElementById('networkTitle').textContent='Connect to '+params.ssid;const passwordGroup=document.getElementById('passwordGroup');if(!params.secure){passwordGroup.style.display='none';console.log('Network is open, hiding password field');}else{passwordGroup.style.display='block';console.log('Network is secured, showing password field');}setActiveNav('/wifi');});
</script>
)=====";

// WiFi hidden network page template
const char WebServerManager::wifiHiddenTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1 class='wifi-title' style='margin-bottom: 20px;'>Connect to Hidden Network</h1>
<div class='separator-line'></div>
<div class='form-group'>
<label for='hiddenSSID'>Network Name (SSID):</label>
<input type='text' id='hiddenSSID' placeholder='Enter network name'>
</div>
<div class='form-group'>
<label for='hiddenPassword'>Password:</label>
<div class='password-container'>
<input type='password' id='hiddenPassword' placeholder='Enter password'>
<button type='button' class='password-toggle' onclick='togglePasswordVisibility()'>Show</button>
</div>
</div>
<div class='form-buttons' style='margin-top: 25px;'>
<div class='button-row'>
<button onclick='connectToHiddenNetwork()' class='connect-button' id='connectBtn'>Connect</button>
<button onclick='goBack()' class='cancel-button'>Cancel</button>
</div>
</div>
</div>
<script>
function togglePasswordVisibility(){const i=document.getElementById('hiddenPassword'),b=i.nextElementSibling;if(i.type==='password'){i.type='text';b.textContent='Hide';}else{i.type='password';b.textContent='Show';}}
function connectToHiddenNetwork(){const ssid=document.getElementById('hiddenSSID').value;const password=document.getElementById('hiddenPassword').value;const btn=document.getElementById('connectBtn');if(!ssid){btn.innerHTML='Please enter network name';btn.style.backgroundColor='#dc3545';setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);return;}btn.innerHTML='<span class="loading-spinner"></span> Connecting';btn.disabled=true;fetch('/api/wifi/connect',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'ssid='+encodeURIComponent(ssid)+'&password='+encodeURIComponent(password)}).then(r=>r.json()).then(d=>{if(d.success){window.location.href='/wifi';}else{btn.innerHTML='Failed: '+(d.message||'Connection failed');btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);}}).catch(()=>{btn.innerHTML='Connection failed';btn.style.backgroundColor='#dc3545';btn.disabled=false;setTimeout(()=>{btn.innerHTML='Connect';btn.style.backgroundColor='#0066cc';},3000);});}
function goBack(){window.location.href='/wifi';}
window.addEventListener('load',function(){setActiveNav('/wifi');});
</script>
)=====";

// AP Control page template
const char WebServerManager::apControlTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Access Point Control</h1>
<div id='apContent'>Loading...</div>
</div>
<script>
function loadAPInterface() {
    fetch('/api/ap/status').then(r => r.json()).then(d => {
        document.getElementById('apContent').innerHTML =
            '<div class="info-grid">' +
            '<div class="info-item"><span class="info-label">AP Status:</span><span class="info-value">' + (d.enabled ? 'Active' : 'Inactive') + '</span></div>' +
            '<div class="info-item"><span class="info-label">AP Name (SSID):</span><span class="info-value">' + (d.ssid || 'Unknown') + '</span></div>' +
            '<div class="info-item"><span class="info-label">Connected Clients:</span><span class="info-value">' + (d.clientCount || 0) + ' device(s)</span></div>' +
            '<div class="info-item"><span class="info-label">IP Address:</span><span class="info-value">' + (d.ip || 'Not available') + '</span></div>' +
            '</div>' +
            '<h1 class="wifi-title" style="margin-bottom: 20px; margin-top: 30px;">Change AP Configuration</h1>' +
            '<div class="separator-line"></div>' +
            '<div class="form-group">' +
            '<label for="currentPassword">Current Password:</label>' +
            '<div class="password-container">' +
            '<input type="password" id="currentPassword" placeholder="Enter current AP password">' +
            '<button type="button" class="password-toggle" onclick="togglePasswordVisibility(\'currentPassword\')">Show</button>' +
            '</div>' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="newSSID">New AP Name (SSID):</label>' +
            '<input type="text" id="newSSID" placeholder="Enter new AP name" value="' + (d.ssid || '') + '">' +
            '</div>' +
            '<div class="form-group">' +
            '<label for="newPassword">New Password:</label>' +
            '<div class="password-container">' +
            '<input type="password" id="newPassword" placeholder="Enter new password">' +
            '<button type="button" class="password-toggle" onclick="togglePasswordVisibility(\'newPassword\')">Show</button>' +
            '</div>' +
            '<div style="color: #ffc107; font-size: 12px; margin-top: 5px;">⚠️ Password must be at least 8 characters long</div>' +
            '</div>' +
            '<div class="form-buttons" style="margin-top: 25px;">' +
            '<div class="button-row">' +
            '<button onclick="updateAPConfig()" class="connect-button" id="updateBtn">Update AP Configuration</button>' +
            '<button onclick="goBack()" class="cancel-button">Cancel</button>' +
            '</div>' +
            '</div>';
    }).catch(e => {
        document.getElementById('apContent').innerHTML = '<div class="info-grid"><div class="info-item"><span class="info-label">Error:</span><span class="info-value">Failed to load AP information</span></div></div>';
    });
}

function updateAPConfig() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newSSID = document.getElementById('newSSID').value;
    const newPassword = document.getElementById('newPassword').value;

    if (!currentPassword) {
        alert('Please enter the current AP password');
        return;
    }

    if (!newSSID || newSSID.length < 1) {
        alert('Please enter a valid AP name');
        return;
    }

    if (!newPassword || newPassword.length < 8) {
        alert('Password must be at least 8 characters long');
        return;
    }

    const btn = document.getElementById('updateBtn');
    btn.textContent = 'Updating...';
    btn.disabled = true;

    fetch('/api/ap/config', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'currentPassword=' + encodeURIComponent(currentPassword) +
              '&newSSID=' + encodeURIComponent(newSSID) +
              '&newPassword=' + encodeURIComponent(newPassword)
    }).then(r => r.json()).then(d => {
        if (d.success) {
            alert('AP configuration updated successfully! The device will restart.');
            setTimeout(() => window.location.reload(), 2000);
        } else {
            alert('Failed to update AP configuration: ' + (d.error || 'Unknown error'));
            btn.textContent = 'Update AP Configuration';
            btn.disabled = false;
        }
    }).catch(e => {
        alert('Network error: ' + e.message);
        btn.textContent = 'Update AP Configuration';
        btn.disabled = false;
    });
}

function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;

    if (input.type === 'password') {
        input.type = 'text';
        button.textContent = 'Hide';
    } else {
        input.type = 'password';
        button.textContent = 'Show';
    }
}

function goBack() {
    window.history.back();
}

window.addEventListener('load', function() { setActiveNav('/ap-control'); loadAPInterface(); });
</script>
)=====";

// Device control page template
const char WebServerManager::deviceTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Device Control</h1>
<p>Device control functionality will be implemented in a future update.</p>
<div class='button-container'>
<a href='/' class='btn btn-secondary'>← Back to Dashboard</a>
</div>
</div>
<script>window.addEventListener('load', function() { setActiveNav('/device'); });</script>
)=====";

// MQTT status page template
const char WebServerManager::mqttTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>MQTT Status</h1>
<p>MQTT status functionality will be implemented in a future update.</p>
<div class='button-container'>
<a href='/' class='btn btn-secondary'>← Back to Dashboard</a>
</div>
</div>
<script>window.addEventListener('load', function() { setActiveNav('/mqtt'); });</script>
)=====";

// System Update page template (compact)
const char WebServerManager::systemUpdateTemplate[] PROGMEM = R"=====(
<div class='content-section system-update'>
<h1>System Update</h1>
<div id='updateContent'>Loading...</div>
</div>
<script>
function loadUpdateInterface() {
    fetch('/api/system-update').then(r => r.json()).then(d => {
        document.getElementById('updateContent').innerHTML =
            '<div class="info-card"><h3>Current Version</h3><p>' + (d.currentVersion || '1.0.0') + '</p></div>' +
            '<div class="info-card"><h3>Update Status</h3><p id="updateStatus">' + (d.updateStatus || 'Click Check for Updates') + '</p></div>' +
            '<div class="info-card"><h3>Auto Update</h3><div class="toggle-switch-container"><label class="toggle-switch"><input type="checkbox" id="autoToggle"' + (d.autoUpdate ? ' checked' : '') + '><span class="toggle-slider"></span></label><span class="toggle-label">Enable automatic updates</span></div></div>' +
            '<div class="button-container"><button class="btn btn-primary" onclick="checkUpdates()" style="width:100%">Check for Updates</button></div>' +
            '<div id="updateDetails" style="display:none"><div class="info-card" style="border-left:4px solid #28a745"><h3>Update Available</h3>' +
            '<p><strong>New Version:</strong> <span id="newVersion"></span></p><p><strong>Release Notes:</strong></p><p id="releaseNotes"></p>' +
            '<div class="button-container"><button class="btn btn-success" onclick="installUpdate()">Install Update</button></div></div></div>' +
            '<div id="updateProgress" style="display:none"><div class="info-card" style="border-left:4px solid #ffa500"><h3>Update in Progress</h3>' +
            '<p id="progressText">Preparing update...</p><div style="width:100%;height:20px;background:#333;border-radius:10px;margin:10px 0">' +
            '<div id="progressBar" style="height:100%;background:#0066cc;width:0%;border-radius:10px;transition:width 0.3s"></div></div>' +
            '<p style="font-size:0.9em;color:#ffa500">⚠️ Do not power off the device during update</p></div></div>';
        document.getElementById('autoToggle').onchange = toggleAuto;
    }).catch(e => document.getElementById('updateContent').innerHTML = '<p>Error loading update interface</p>');
}

function checkUpdates() {
    const s = document.getElementById('updateStatus'), d = document.getElementById('updateDetails');
    s.textContent = 'Checking...'; d.style.display = 'none';
    fetch('/api/update-check', { method: 'POST' }).then(r => r.json()).then(data => {
        if (data.error) s.textContent = 'Server not available';
        else if (data.updateAvailable) {
            s.textContent = 'Update available';
            document.getElementById('newVersion').textContent = data.version;
            document.getElementById('releaseNotes').textContent = data.releaseNotes;
            d.style.display = 'block';
        } else s.textContent = 'No updates available';
    }).catch(e => s.textContent = 'Server not available');
}

function installUpdate() {
    const btn = event.target;

    btn.textContent = 'Installing...';
    btn.disabled = true;

    fetch('/api/install-update', { method: 'POST' })
        .then(r => r.json())
        .then(d => {
            if (d.error) {
                // Show error and restore button
                const s = document.getElementById('updateStatus');
                const det = document.getElementById('updateDetails');
                const prog = document.getElementById('updateProgress');

                s.textContent = 'Installation failed: ' + d.error;
                prog.style.display = 'none';
                det.style.display = 'block';
                btn.disabled = false;
                btn.textContent = 'Install Update';
            } else {
                // Show update message
                showUpdateMessage();
            }
        })
        .catch(e => {
            // Show error and restore button
            const s = document.getElementById('updateStatus');
            const det = document.getElementById('updateDetails');
            const prog = document.getElementById('updateProgress');

            s.textContent = 'Installation failed - Network error';
            prog.style.display = 'none';
            det.style.display = 'block';
            btn.disabled = false;
            btn.textContent = 'Install Update';
        });
}



function toggleAuto() {
    const enabled = document.getElementById('autoToggle').checked;
    fetch('/api/auto-update-toggle', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ enabled: enabled }) })
    .then(r => r.json()).then(d => { if (!d.success) { document.getElementById('autoToggle').checked = !enabled; alert('Failed to update setting'); } })
    .catch(e => { document.getElementById('autoToggle').checked = !enabled; alert('Failed to update setting'); });
}
window.addEventListener('load', () => { setActiveNav('/system-update'); loadUpdateInterface(); });
</script>
)=====";

// 404 error page template
const char WebServerManager::notFoundTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Page Not Found</h1>
<p>The requested page could not be found.</p>
<div class='button-container'>
<a href='/' class='btn btn-primary'>← Back to Dashboard</a>
</div>
</div>
)=====";

// Implementation of WebServerManager methods
void WebServerManager::sendHtmlPage(const String &content)
{
    // Calculate required size more accurately
    size_t headerSize = strlen_P(baseHeader);
    size_t footerSize = strlen_P(baseFooter);
    size_t totalSize = headerSize + content.length() + footerSize + 100; // +100 for safety margin

    // Check if we have enough memory for the complete page
    if (ESP.getFreeHeap() < totalSize + 2048) // Need extra 2KB for operations
    {
        Serial.print("Insufficient memory for page - Required: ");
        Serial.print(totalSize);
        Serial.print(", Available: ");
        Serial.println(ESP.getFreeHeap());
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Insufficient Memory");
        return;
    }

    String html;
    html.reserve(totalSize); // Reserve exact space needed

    // Read header and footer from PROGMEM and add dynamic content
    html = FPSTR(baseHeader);
    html += content;
    html += FPSTR(baseFooter);

    _server.send(200, "text/html", html);
}

void WebServerManager::sendProgmemPage(const char *progmemTemplate, const char *pageHeader)
{
    // Calculate required size for PROGMEM template
    size_t baseHeaderSize = strlen_P(baseHeader);
    size_t pageHeaderSize = pageHeader ? strlen_P(pageHeader) : 0;
    size_t templateSize = strlen_P(progmemTemplate);
    size_t footerSize = strlen_P(baseFooter);
    size_t totalSize = baseHeaderSize + pageHeaderSize + templateSize + footerSize + 100; // +100 for safety margin

    // Check if we have enough memory
    if (ESP.getFreeHeap() < totalSize + 2048) // Need extra 2KB for operations
    {
        Serial.print("Insufficient memory for PROGMEM page - Required: ");
        Serial.print(totalSize);
        Serial.print(", Available: ");
        Serial.println(ESP.getFreeHeap());
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Insufficient Memory");
        return;
    }

    String html;
    html.reserve(totalSize);

    // Build page from PROGMEM templates
    html = FPSTR(baseHeader);
    if (pageHeader)
    {
        html += FPSTR(pageHeader);
    }
    html += FPSTR(progmemTemplate);
    html += FPSTR(baseFooter);

    // Debug: Log actual response size
    Serial.print("Sending HTML response - Expected size: ");
    Serial.print(totalSize);
    Serial.print(", Actual size: ");
    Serial.print(html.length());
    Serial.print(", Free heap: ");
    Serial.println(ESP.getFreeHeap());

    _server.send(200, "text/html", html);

    // Small delay to ensure response is sent
    delay(10);
    yield();
}

void WebServerManager::begin()
{
    if (_serverRunning)
    {
        Serial.println("WARNING: Web server already running, skipping initialization");
        return;
    }

    Serial.println("=== Starting Web Server ===");

    // Main page
    _server.on("/", [this]()
               { handleRoot(); });

    // Device control routes
    _server.on("/device", [this]()
               { handleDevice(); });
    _server.on("/switches", [this]()
               { handleSwitches(); });
    _server.on("/switch", HTTP_GET, [this]()
               { handleSwitch(); });
    _server.on("/switch", HTTP_POST, [this]()
               { handleSwitch(); });
    _server.on("/rgb/off", [this]()
               { handleRGBOff(); });
    _server.on("/rgb/on", [this]()
               { handleRGBOn(); });

    // ESP32 RGB API routes
#ifdef IS_ESP32
    _server.on("/api/rgb/set", HTTP_POST, [this]()
               { handleRGBSet(); });
    _server.on("/api/rgb/preset", HTTP_POST, [this]()
               { handleRGBPreset(); });
    _server.on("/api/rgb/brightness", HTTP_POST, [this]()
               { handleRGBBrightness(); });
    _server.on("/api/rgb/cycle/mode", HTTP_POST, [this]()
               { handleColorCycleMode(); });
    _server.on("/api/rgb/cycle/speed", HTTP_POST, [this]()
               { handleColorCycleSpeed(); });
    _server.on("/api/rgb/cycle/saturation", HTTP_POST, [this]()
               { handleColorCycleSaturation(); });
    _server.on("/api/rgb/cycle/mask", HTTP_POST, [this]()
               { handleColorCycleMask(); });
#endif

    // WiFi and MQTT routes
    _server.on("/wifi", [this]()
               { handleWiFi(); });
    _server.on("/wifi/connect", [this]()
               { handleWiFiConnectPage(); });
    _server.on("/wifi/hidden", [this]()
               { handleWiFiHiddenPage(); });
    _server.on("/ap-control", [this]()
               { handleAPControl(); });
    _server.on("/mqtt", [this]()
               { handleMqtt(); });
    _server.on("/system-update", [this]()
               { handleSystemUpdate(); });

#ifdef COMPILE_TEMPERATURE_SENSOR
    _server.on("/temperature", [this]()
               { handleTemperature(); });
#endif

    // WiFi API endpoints
    _server.on("/api/wifi/scan", [this]()
               { handleWiFiScan(); });
    _server.on("/api/wifi/connect", HTTP_POST, [this]()
               { handleWiFiConnect(); });

    // AP Control API endpoints
    _server.on("/api/ap/status", [this]()
               { handleAPStatus(); });
    _server.on("/api/ap/config", HTTP_POST, [this]()
               { handleAPConfig(); });

    // Status API endpoint for real-time updates
    _server.on("/api/status", [this]()
               { handleStatusAPI(); });

    // Switches API endpoint for dynamic content
    _server.on("/api/switches", [this]()
               { handleSwitchesAPI(); });

    // System Update API endpoints
    _server.on("/api/system-update", [this]()
               { handleSystemUpdateAPI(); });
    _server.on("/api/update-check", HTTP_POST, [this]()
               { handleUpdateCheck(); });
    _server.on("/api/auto-update-toggle", HTTP_POST, [this]()
               { handleUpdateToggle(); });
    _server.on("/api/install-update", HTTP_POST, [this]()
               { handleInstallUpdate(); });
    _server.on("/api/dashboard-updates", [this]()
               { handleDashboardSSE(); });
    _server.on("/api/switches/events", [this]()
               { handleSwitchesSSE(); });
#ifdef COMPILE_TEMPERATURE_SENSOR
    _server.on("/api/temperature/events", [this]()
               { handleTemperatureSSE(); });
#endif

    // Favicon handler (prevents 404 errors)
    _server.on("/favicon.ico", [this]()
               { _server.send(204); }); // No content

    // 404 handler
    _server.onNotFound([this]()
                       { handle404(); });

    _server.begin();
    _serverRunning = true;

    Serial.println("Web server started successfully");
    Serial.print("Web server running on port ");
    Serial.println(_port);
    Serial.println("=== Web Server Ready ===");
}

void WebServerManager::handleClient()
{
    if (_serverRunning)
    {
        // Check memory before handling client
        if (hasEnoughMemory(3072))
        {
            _server.handleClient();

            // Update SSE connections with periodic data
            updateSSEConnections();

            yield(); // Allow other tasks to run
#ifndef IS_ESP32
            ESP.wdtFeed(); // Feed watchdog timer (ESP8266 only)
#endif
        }
        else
        {
            Serial.println("WARNING: Skipping client handling due to low memory");
            uint32_t freeHeap = ESP.getFreeHeap();
            Serial.print("Free heap: ");
            Serial.print(freeHeap);
            Serial.println(" bytes");
            delay(100); // Brief delay to allow memory recovery
        }
    }
    else
    {
        // This should not happen, but let's log it if it does
        static unsigned long lastWarning = 0;
        unsigned long currentMillis = millis();
        if (currentMillis - lastWarning >= 10000) // Log every 10 seconds max
        {
            lastWarning = currentMillis;
            Serial.println("WARNING: handleClient called but server not running");
        }
    }
}

bool WebServerManager::isRunning()
{
    return _serverRunning;
}

void WebServerManager::notifyStateChange()
{
    _stateChanged = true;
    _lastStateChange = millis();

    // Send immediate updates to active SSE connections
    if (_switchesSSEActive)
    {
        sendSwitchesSSEUpdate();
    }
}

void WebServerManager::stop()
{
    _server.stop();
    _serverRunning = false;
    Serial.println("Web server stopped");
}

// Memory management functions
void WebServerManager::checkMemoryUsage(const String &context)
{
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 5120) // Less than 5KB free
    {
        Serial.print("WARNING: Low memory in ");
        Serial.print(context);
        Serial.print(" - Free heap: ");
        Serial.println(freeHeap);
    }
    else
    {
        Serial.print("Memory OK in ");
        Serial.print(context);
        Serial.print(" - Free heap: ");
        Serial.println(freeHeap);
    }
}

bool WebServerManager::hasEnoughMemory(uint32_t requiredBytes)
{
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < requiredBytes)
    {
        Serial.print("Insufficient memory - Required: ");
        Serial.print(requiredBytes);
        Serial.print(", Available: ");
        Serial.println(freeHeap);
        return false;
    }
    return true;
}

bool WebServerManager::isRequestThrottled(bool isScan)
{
    unsigned long currentTime = millis();
    String clientIP = _server.client().remoteIP().toString();

    if (isScan)
    {
        if (currentTime - _lastScanRequest < SCAN_THROTTLE_MS)
        {
            Serial.println("Scan request throttled");
            return true;
        }
        _lastScanRequest = currentTime;
    }
    else
    {
        if (currentTime - _lastPageRequest < PAGE_THROTTLE_MS && _currentRequestingIP == clientIP)
        {
            Serial.println("Page request throttled for IP: " + clientIP);
            return true;
        }
        _lastPageRequest = currentTime;
        _currentRequestingIP = clientIP;
    }

    return false;
}

// Handle root page (dashboard)
void WebServerManager::handleRoot()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("Dashboard page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before dashboard: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("Dashboard rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send dashboard using PROGMEM template (no string building!)
    sendProgmemPage(dashboardTemplate, dashboardHeader);

    Serial.print("Free heap after dashboard: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("Dashboard page sent successfully");
}

// Handle device control page (simplified for 3-Relay)
void WebServerManager::handleDevice()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    Serial.println("Device page requested");

    // Send device page using PROGMEM template (no string building!)
    sendProgmemPage(deviceTemplate, dashboardHeader);

    Serial.println("Device page sent successfully");
}

// Handle switches control page
void WebServerManager::handleSwitches()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    Serial.print(DEVICE_TYPE);
    Serial.println(" switches page requested");

    // Use device-specific templates
#ifdef IS_ESP32
    sendProgmemPage(ESP32_switchesTemplate, switchesHeader);
#else
#ifdef DEVICE_MODEL_SCENARIO_KEY
    sendProgmemPage(ESP8266_scenarioTemplate, switchesHeader);
#else
    sendProgmemPage(ESP8266_switchesTemplate, switchesHeader);
#endif
#endif

    Serial.print(DEVICE_TYPE);
    Serial.println(" switches page sent successfully");
}

// Handle switches API - returns just the switch cards HTML
void WebServerManager::handleSwitchesAPI()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    // Check memory before building HTML response
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 6144) // Need at least 6KB for HTML building
    {
        Serial.print("Switches API rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    Serial.print("Switches API - Free heap: ");
    Serial.println(freeHeap);

    // Build minimal switch cards HTML
    String html = "";

    for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
    {
        bool state = _deviceManager->getSwitchState(i);
        bool rOff, gOff, bOff, rOn, gOn, bOn;
        _deviceManager->getRGBOff(i, rOff, gOff, bOff);
        _deviceManager->getRGBOn(i, rOn, gOn, bOn);
        String offColor = getColorNameFromRGB(rOff, gOff, bOff);
        String onColor = getColorNameFromRGB(rOn, gOn, bOn);

        html += "<div class='switch-card'>";
        html += "<div class='switch-header'>";
        html += "<div class='switch-title'>Switch " + String(i + 1) + "</div>";
        html += "<span class='switch-state " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
        html += "</div>";
        html += "<div class='toggle-container'>";
        html += "<span>Toggle Switch:</span>";
        html += "<div class='toggle-switch " + String(state ? "on" : "off") + "' onclick='toggleSwitch(" + String(i) + ")'></div>";
        html += "</div>";
        html += "<div class='color-controls'>";
        html += "<div class='color-group'><h4>OFF LED</h4>";
        html += "<select class='color-dropdown " + offColor + "' data-switch='" + String(i) + "' data-state='off'>";
        html += "<option value='off'>Off</option><option value='red'>Red</option><option value='green'>Green</option>";
        html += "<option value='blue'>Blue</option><option value='yellow'>Yellow</option><option value='cyan'>Cyan</option>";
        html += "<option value='magenta'>Magenta</option><option value='white'>White</option>";
        html += "</select></div>";
        html += "<div class='color-group'><h4>ON LED</h4>";
        html += "<select class='color-dropdown " + onColor + "' data-switch='" + String(i) + "' data-state='on'>";
        html += "<option value='off'>Off</option><option value='red'>Red</option><option value='green'>Green</option>";
        html += "<option value='blue'>Blue</option><option value='yellow'>Yellow</option><option value='cyan'>Cyan</option>";
        html += "<option value='magenta'>Magenta</option><option value='white'>White</option>";
        html += "</select></div>";
        html += "</div>";
        html += "</div>";
    }

    _server.send(200, "text/html", html);
}

// Handle WiFi configuration page
void WebServerManager::handleWiFi()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi page: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("WiFi page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi page using PROGMEM template (no string building!)
    sendProgmemPage(wifiTemplate, wifiHeader);

    Serial.print("Free heap after WiFi page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi page sent successfully");
}

// Handle WiFi connect page
void WebServerManager::handleWiFiConnectPage()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi connect page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi connect page: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("WiFi connect page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi connect page using PROGMEM template
    sendProgmemPage(wifiConnectTemplate, wifiHeader);

    Serial.print("Free heap after WiFi connect page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi connect page sent successfully");
}

// Handle WiFi hidden network page
void WebServerManager::handleWiFiHiddenPage()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("WiFi hidden network page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before WiFi hidden page: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("WiFi hidden page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send WiFi hidden page using PROGMEM template
    sendProgmemPage(wifiHiddenTemplate, wifiHeader);

    Serial.print("Free heap after WiFi hidden page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("WiFi hidden page sent successfully");
}

// Handle MQTT status page
void WebServerManager::handleMqtt()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("MQTT page requested");

    // Send MQTT page using PROGMEM template (no string building!)
    sendProgmemPage(mqttTemplate, mqttHeader);

    Serial.println("MQTT page sent successfully");
}

// Handle switch control
void WebServerManager::handleSwitch()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("state"))
        {
            int index = _server.arg("index").toInt();
            bool state = _server.arg("state").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setSwitchState(index, state);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle RGB OFF color setting
void WebServerManager::handleRGBOff()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("r") && _server.hasArg("g") && _server.hasArg("b"))
        {
            int index = _server.arg("index").toInt();
            bool r = _server.arg("r").toInt() == 1;
            bool g = _server.arg("g").toInt() == 1;
            bool b = _server.arg("b").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setRGBOff(index, r, g, b);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle RGB ON color setting
void WebServerManager::handleRGBOn()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    if (_server.method() == HTTP_POST)
    {
        if (_server.hasArg("index") && _server.hasArg("r") && _server.hasArg("g") && _server.hasArg("b"))
        {
            int index = _server.arg("index").toInt();
            bool r = _server.arg("r").toInt() == 1;
            bool g = _server.arg("g").toInt() == 1;
            bool b = _server.arg("b").toInt() == 1;

            if (index >= 0 && index < _deviceManager->getSwitchCount())
            {
                _deviceManager->setRGBOn(index, r, g, b);
                _server.send(200, "text/plain", "OK");
                return;
            }
        }
    }

    _server.send(400, "text/plain", "Bad Request");
}

// Handle WiFi scan API
void WebServerManager::handleWiFiScan()
{
    if (isRequestThrottled(true)) // Use scan throttling
    {
        _server.send(429, "application/json", "{\"error\":\"Too many scan requests\"}");
        return;
    }

    Serial.println("WiFi scan requested");

    // Check memory before scanning
    if (!hasEnoughMemory(5120))
    {
        _server.send(503, "application/json", "{\"error\":\"Insufficient memory for scan\"}");
        return;
    }

    // Perform WiFi scan
    int networkCount = WiFi.scanNetworks();

    String json = "{\"networks\":[";

    if (networkCount > 0)
    {
        for (int i = 0; i < networkCount && i < 20; i++) // Limit to 20 networks
        {
            if (i > 0)
                json += ",";

            json += "{";
            json += "\"ssid\":\"" + WiFi.SSID(i) + "\",";
            json += "\"rssi\":" + String(WiFi.RSSI(i)) + ",";
#ifdef IS_ESP32
            json += "\"encryption\":\"" + String(WiFi.encryptionType(i) == WIFI_AUTH_OPEN ? "none" : "secured") + "\"";
#else
            json += "\"encryption\":\"" + String(WiFi.encryptionType(i) == ENC_TYPE_NONE ? "none" : "secured") + "\"";
#endif
            json += "}";
        }
    }

    json += "]}";

    // Clean up scan results
    WiFi.scanDelete();

    _server.send(200, "application/json", json);
    Serial.println("WiFi scan completed, found " + String(networkCount) + " networks");
}

// Handle WiFi connect API
void WebServerManager::handleWiFiConnect()
{
    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"message\":\"Method not allowed\"}");
        return;
    }

    if (!_server.hasArg("ssid"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"SSID required\"}");
        return;
    }

    String ssid = _server.arg("ssid");
    String password = _server.hasArg("password") ? _server.arg("password") : "";

    Serial.println("WiFi connect request for SSID: " + ssid);

    if (ssid.length() == 0)
    {
        _server.send(400, "application/json", "{\"success\":false,\"message\":\"SSID cannot be empty\"}");
        return;
    }

    // Use WiFiManager if available, otherwise use direct WiFi connection
    bool success = false;
    String message = "";

    if (_wifiManager)
    {
        success = _wifiManager->connect(ssid, password);
        if (success)
        {
            message = "Connected successfully";
            // Save credentials to EEPROM on successful connection
            _wifiManager->saveCredentials(ssid, password);
        }
        else
        {
            message = "Failed to connect to network";
        }
    }
    else
    {
        // Fallback to direct WiFi connection
        WiFi.begin(ssid.c_str(), password.c_str());

        // Wait up to 10 seconds for connection
        int attempts = 0;
        while (WiFi.status() != WL_CONNECTED && attempts < 20)
        {
            delay(500);
            attempts++;
            yield(); // Allow other tasks to run
        }

        success = (WiFi.status() == WL_CONNECTED);
        if (success)
        {
            message = "Connected successfully";
        }
        else
        {
            message = "Failed to connect - check credentials";
        }
    }

    String json = "{\"success\":" + String(success ? "true" : "false") + ",\"message\":\"" + message + "\"}";
    _server.send(200, "application/json", json);

    Serial.println("WiFi connect result: " + String(success ? "SUCCESS" : "FAILED"));
}

// Handle status API for real-time updates
void WebServerManager::handleStatusAPI()
{
    if (!_deviceManager)
    {
        _server.send(404, "application/json", "{\"error\":\"Device manager not initialized\"}");
        return;
    }

    // Check memory before building JSON response
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 5120) // Need at least 5KB for JSON building
    {
        Serial.print("Status API rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "application/json", "{\"error\":\"Low memory\"}");
        return;
    }

    Serial.print("Status API - Free heap: ");
    Serial.println(freeHeap);

    String json = "{";

    // WiFi status (flat structure for dashboard compatibility)
    bool wifiConnected = (_wifiManager && _wifiManager->isConnected()) || (WiFi.status() == WL_CONNECTED);
    json += "\"wifiConnected\":" + String(wifiConnected ? "true" : "false");

    if (wifiConnected)
    {
        String ssid = "";
        int32_t rssi = 0;

        if (_wifiManager)
        {
            ssid = _wifiManager->getSSID();
            rssi = WiFi.RSSI();
        }
        else
        {
            ssid = WiFi.SSID();
            rssi = WiFi.RSSI();
        }

        json += ",\"ssid\":\"" + ssid + "\"";
        json += ",\"rssi\":" + String(rssi);
        json += ",\"ipAddress\":\"" + WiFi.localIP().toString() + "\"";
    }

    // Device information (flat structure for dashboard compatibility)
    String deviceID = _deviceManager->getDeviceID();
    String deviceName = _deviceManager->getDeviceName();
    String deviceType = _deviceManager->getDeviceType();
    uint8_t switchCount = _deviceManager->getSwitchCount();

    if (deviceName.length() == 0)
        deviceName = "Unknown";

    json += ",\"deviceID\":\"" + deviceID + "\"";
    json += ",\"deviceName\":\"" + deviceName + "\"";
    json += ",\"deviceType\":\"" + deviceType + "\"";
    json += ",\"switchCount\":" + String(switchCount);

    // System information
    json += ",\"freeHeap\":" + String(ESP.getFreeHeap());

    // Calculate uptime
    unsigned long uptimeMs = millis();
    unsigned long uptimeSeconds = uptimeMs / 1000;
    unsigned long days = uptimeSeconds / 86400;
    unsigned long hours = (uptimeSeconds % 86400) / 3600;
    unsigned long minutes = (uptimeSeconds % 3600) / 60;
    unsigned long seconds = uptimeSeconds % 60;

    String uptime = "";
    if (days > 0)
        uptime += String(days) + "d ";
    if (hours > 0)
        uptime += String(hours) + "h ";
    if (minutes > 0)
        uptime += String(minutes) + "m ";
    uptime += String(seconds) + "s";

    json += ",\"uptime\":\"" + uptime + "\"";

    // Switch states (for switches page compatibility)
    json += ",\"switchState\":[";
    for (uint8_t i = 0; i < switchCount; i++)
    {
        if (i > 0)
            json += ",";
        json += _deviceManager->getSwitchState(i) ? "true" : "false";
    }
    json += "]";

    // RGB color states for switches page
    json += ",\"rgbOffColors\":[";
    for (uint8_t i = 0; i < switchCount; i++)
    {
        if (i > 0)
            json += ",";
        bool r, g, b;
        _deviceManager->getRGBOff(i, r, g, b);
        String colorName = getColorNameFromRGB(r, g, b);
        json += "\"" + colorName + "\"";
    }
    json += "]";

    json += ",\"rgbOnColors\":[";
    for (uint8_t i = 0; i < switchCount; i++)
    {
        if (i > 0)
            json += ",";
        bool r, g, b;
        _deviceManager->getRGBOn(i, r, g, b);
        String colorName = getColorNameFromRGB(r, g, b);
        json += "\"" + colorName + "\"";
    }
    json += "]";

    json += "}";

    // Reset state changed flag after reporting
    _stateChanged = false;

    _server.send(200, "application/json", json);
}

// Handle 404 errors
void WebServerManager::handle404()
{
    String uri = _server.uri();
    String method = (_server.method() == HTTP_GET) ? "GET" : "POST";

    Serial.print("404 ERROR - ");
    Serial.print(method);
    Serial.print(" ");
    Serial.print(uri);
    Serial.println(" not found");

    // Send 404 page using PROGMEM template (no string building!)
    sendProgmemPage(notFoundTemplate, dashboardHeader);

    Serial.println("404 page sent successfully");
}

// Handle System Update page
void WebServerManager::handleSystemUpdate()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("System Update page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before System Update page: ");
    Serial.println(freeHeap);

    if (freeHeap < 6144)
    {
        Serial.println("System Update page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send System Update page using PROGMEM template
    sendProgmemPage(systemUpdateTemplate, systemUpdateHeader);

    Serial.print("Free heap after System Update page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("System Update page sent successfully");
}

#ifdef COMPILE_TEMPERATURE_SENSOR
// Handle Temperature Sensor page
void WebServerManager::handleTemperature()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    // Always show temperature page, even if sensor is not initialized
    // The page will show "sensor not responding" status

    Serial.println("Temperature page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before Temperature page: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("Temperature page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send Temperature page using PROGMEM template
    sendProgmemPage(temperatureTemplate, temperatureHeader);

    Serial.print("Free heap after Temperature page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("Temperature page sent successfully");
}
#endif

// Handle System Update API (get current status)
void WebServerManager::handleSystemUpdateAPI()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    Serial.println("System Update API requested");

    // Check if auto-update is enabled (stored in EEPROM)
    bool autoUpdate = EEPROM.read(500) == 1; // Use address 500 for auto-update setting

    String response = "{";
    response += "\"currentVersion\":\"1.0.0\",";
    response += "\"deviceModel\":\"ESP8266_Switch\",";
    response += "\"autoUpdate\":" + String(autoUpdate ? "true" : "false") + ",";
    response += "\"updateStatus\":\"Click 'Check for Updates' to check status\"";
    response += "}";

    Serial.print("Sending response: ");
    Serial.println(response);

    _server.send(200, "application/json", response);
}

// Handle update check request
void WebServerManager::handleUpdateCheck()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    if (!WiFi.isConnected())
    {
        _server.send(200, "application/json", "{\"error\":\"WiFi not connected\"}");
        return;
    }

    Serial.println("Manual update check requested via web interface");

    // Get update information from OTA manager
    OTAManager::UpdateInfo updateInfo = _otaManager->getUpdateInfo();

    String response = "{";
    if (!updateInfo.error.isEmpty())
    {
        response += "\"error\":\"" + updateInfo.error + "\"";
    }
    else if (updateInfo.available)
    {
        response += "\"updateAvailable\":true,";
        response += "\"version\":\"" + updateInfo.version + "\",";
        response += "\"releaseNotes\":\"" + updateInfo.releaseNotes + "\"";
    }
    else
    {
        response += "\"updateAvailable\":false,";
        response += "\"message\":\"No updates available\"";
    }
    response += "}";

    _server.send(200, "application/json", response);
}

// Handle auto-update toggle
void WebServerManager::handleUpdateToggle()
{
    if (_server.hasArg("plain"))
    {
        String body = _server.arg("plain");

        // Simple JSON parsing for enabled field
        bool enabled = body.indexOf("\"enabled\":true") != -1;

        // Store auto-update setting in EEPROM
        EEPROM.write(500, enabled ? 1 : 0);
        EEPROM.commit();

        Serial.print("Auto-update setting changed to: ");
        Serial.println(enabled ? "enabled" : "disabled");

        _server.send(200, "application/json", "{\"success\":true}");
    }
    else
    {
        _server.send(400, "application/json", "{\"error\":\"Invalid request\"}");
    }
}

// Handle install update request
void WebServerManager::handleInstallUpdate()
{
    if (!_otaManager)
    {
        _server.send(404, "application/json", "{\"error\":\"OTA manager not initialized\"}");
        return;
    }

    if (!WiFi.isConnected())
    {
        _server.send(200, "application/json", "{\"error\":\"WiFi not connected\"}");
        return;
    }

    Serial.println("Manual update installation requested via web interface");

    // First check if update is available
    OTAManager::UpdateInfo updateInfo = _otaManager->getUpdateInfo();

    if (!updateInfo.error.isEmpty())
    {
        _server.send(200, "application/json", "{\"error\":\"" + updateInfo.error + "\"}");
        return;
    }

    if (!updateInfo.available)
    {
        _server.send(200, "application/json", "{\"error\":\"No updates available\"}");
        return;
    }

    // Send immediate response
    _server.send(200, "application/json", "{\"success\":true,\"message\":\"Update started\"}");

    // Small delay to ensure response is sent
    delay(100);

    // Trigger the actual update installation (bypasses auto-update setting)
    _otaManager->performManualUpdate();
}

// Handle SSE endpoint for real-time dashboard updates (memory and uptime)
void WebServerManager::handleDashboardSSE()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    // Check memory before starting SSE
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 4096) // Need at least 4KB for SSE operations
    {
        Serial.print("Dashboard SSE rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Close any existing dashboard SSE connection
    if (_dashboardSSEActive)
    {
        closeSSEConnection(_dashboardSSEClient, _dashboardSSEActive, "Dashboard (replacing)");
    }

    Serial.println("Dashboard SSE connection established");

    // Get the client connection
    _dashboardSSEClient = _server.client();
    _dashboardSSEActive = true;
    _lastDashboardUpdate = millis();

    // Set SSE headers
    _server.setContentLength(CONTENT_LENGTH_UNKNOWN);
    _server.send(200, "text/event-stream", "");
    _server.sendContent("Cache-Control: no-cache\r\n");
    _server.sendContent("Connection: keep-alive\r\n");
    _server.sendContent("Access-Control-Allow-Origin: *\r\n");
    _server.sendContent("\r\n");

    // Send initial connection event
    _server.sendContent("event: connected\r\n");
    _server.sendContent("data: {\"status\":\"connected\"}\r\n\r\n");

    // Send initial update immediately
    sendDashboardSSEUpdate();
}

// Simple SSE endpoint for switches - sends current state and closes
void WebServerManager::handleSwitchesSSE()
{
    if (!_deviceManager)
    {
        _server.send(404, "text/plain", "Device manager not initialized");
        return;
    }

    // Close any existing switches SSE connection
    if (_switchesSSEActive)
    {
        closeSSEConnection(_switchesSSEClient, _switchesSSEActive, "Switches (replacing)");
    }

    Serial.println("Switches SSE connection established");

    // Get the client connection
    _switchesSSEClient = _server.client();
    _switchesSSEActive = true;
    _lastSwitchesUpdate = millis();

    // Set SSE headers
    _server.setContentLength(CONTENT_LENGTH_UNKNOWN);
    _server.send(200, "text/event-stream", "");
    _server.sendContent("Cache-Control: no-cache\r\n");
    _server.sendContent("Connection: keep-alive\r\n");
    _server.sendContent("Access-Control-Allow-Origin: *\r\n");
    _server.sendContent("\r\n");

    // Send initial data immediately
    sendSwitchesSSEUpdate();
}

#ifdef COMPILE_TEMPERATURE_SENSOR
// Handle Temperature SSE endpoint for real-time temperature updates
void WebServerManager::handleTemperatureSSE()
{
    // Handle case where sensor is not initialized
    bool sensorInitialized = (_temperatureSensor != nullptr);

    // Check memory before starting SSE
    uint32_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 4096) // Need at least 4KB for SSE operations
    {
        Serial.print("Temperature SSE rejected due to low memory: ");
        Serial.println(freeHeap);
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Close any existing temperature SSE connection
    if (_temperatureSSEActive)
    {
        closeSSEConnection(_temperatureSSEClient, _temperatureSSEActive, "Temperature (replacing)");
    }

    Serial.println("Temperature SSE connection established");

    // Get the client connection
    _temperatureSSEClient = _server.client();
    _temperatureSSEActive = true;
    _lastTemperatureUpdate = millis();

    // Set SSE headers
    _server.setContentLength(CONTENT_LENGTH_UNKNOWN);
    _server.send(200, "text/event-stream", "");
    _server.sendContent("Cache-Control: no-cache\r\n");
    _server.sendContent("Connection: keep-alive\r\n");
    _server.sendContent("Access-Control-Allow-Origin: *\r\n");
    _server.sendContent("Access-Control-Allow-Headers: Cache-Control\r\n\r\n");

    // Send initial connection event
    _server.sendContent("event: connected\r\n");
    _server.sendContent("data: {\"status\":\"connected\"}\r\n\r\n");

    // Send initial data immediately
    sendTemperatureSSEUpdate();
}
#endif

// Handle AP Control page
void WebServerManager::handleAPControl()
{
    if (isRequestThrottled())
    {
        _server.send(429, "text/plain", "Too Many Requests");
        return;
    }

    Serial.println("AP Control page requested");

    // Check memory before sending PROGMEM page
    uint32_t freeHeap = ESP.getFreeHeap();
    Serial.print("Free heap before AP Control page: ");
    Serial.println(freeHeap);

    if (freeHeap < 4096)
    {
        Serial.println("AP Control page rejected due to low memory");
        _server.send(503, "text/plain", "Service Temporarily Unavailable - Low Memory");
        return;
    }

    // Send AP Control page using PROGMEM template with WiFi header for form styling
    sendProgmemPage(apControlTemplate, wifiHeader);

    Serial.print("Free heap after AP Control page: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("AP Control page sent successfully");
}

// Handle AP status API
void WebServerManager::handleAPStatus()
{
    // Get AP information
    bool apEnabled = WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA;
    String ssid = WiFi.softAPSSID();
    IPAddress ip = WiFi.softAPIP();
    int clientCount = WiFi.softAPgetStationNum();

    // Build JSON response
    String response = "{";
    response += "\"enabled\":" + String(apEnabled ? "true" : "false") + ",";
    response += "\"ssid\":\"" + ssid + "\",";
    response += "\"ip\":\"" + ip.toString() + "\",";
    response += "\"clientCount\":" + String(clientCount);
    response += "}";

    _server.send(200, "application/json", response);
}

// Handle AP configuration update
void WebServerManager::handleAPConfig()
{
    if (!_server.hasArg("currentPassword") || !_server.hasArg("newSSID") || !_server.hasArg("newPassword"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing parameters\"}");
        return;
    }

    String currentPassword = _server.arg("currentPassword");
    String newSSID = _server.arg("newSSID");
    String newPassword = _server.arg("newPassword");

    // Validate new SSID and password
    if (newSSID.length() < 1 || newSSID.length() > 32)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"SSID must be 1-32 characters\"}");
        return;
    }

    if (newPassword.length() < 8 || newPassword.length() > 63)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Password must be 8-63 characters\"}");
        return;
    }

    // Verify current password against stored AP password
    if (_wifiManager)
    {
        String actualCurrentPassword = _wifiManager->getCurrentAPPassword();

        if (currentPassword != actualCurrentPassword)
        {
            Serial.println("AP password verification failed");
            Serial.print("Provided: ");
            Serial.println(currentPassword);
            Serial.print("Expected: ");
            Serial.println(actualCurrentPassword);
            _server.send(400, "application/json", "{\"success\":false,\"error\":\"Current password is incorrect\"}");
            return;
        }

        Serial.println("Current password verified successfully");

        // Update AP configuration in EEPROM
        bool success = _wifiManager->updateAPConfig(currentPassword, newSSID, newPassword);

        if (success)
        {
            Serial.println("AP configuration saved to EEPROM successfully");

            // Send success response first
            _server.send(200, "application/json", "{\"success\":true,\"message\":\"AP configuration updated successfully. Device will restart.\"}");

            // Allow response to be sent
            delay(100);

            // Apply new AP configuration immediately
            Serial.println("Applying new AP configuration...");
            WiFi.softAPdisconnect(true); // Stop current AP
            delay(500);

            // Start AP with new configuration
            bool apStarted = WiFi.softAP(newSSID.c_str(), newPassword.c_str());

            if (apStarted)
            {
                Serial.println("New AP configuration applied successfully");
                Serial.print("New SSID: ");
                Serial.println(newSSID);
                Serial.print("New IP: ");
                Serial.println(WiFi.softAPIP());

                // Restart to ensure all systems use new configuration
                Serial.println("Restarting device in 2 seconds...");
                delay(2000);
                ESP.restart();
            }
            else
            {
                Serial.println("Failed to start AP with new configuration, restarting...");
                delay(1000);
                ESP.restart();
            }
        }
        else
        {
            _server.send(400, "application/json", "{\"success\":false,\"error\":\"Failed to save AP configuration\"}");
        }
    }
    else
    {
        _server.send(500, "application/json", "{\"success\":false,\"error\":\"WiFi manager not available\"}");
    }
}

// ESP32 RGB API handlers
#ifdef IS_ESP32

void WebServerManager::handleRGBSet()
{
    if (!_fullColorRGBManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"RGB manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("switch") || !doc.containsKey("r") || !doc.containsKey("g") || !doc.containsKey("b"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing parameters\"}");
        return;
    }

    uint8_t switchIndex = doc["switch"];
    uint8_t r = doc["r"];
    uint8_t g = doc["g"];
    uint8_t b = doc["b"];

    _fullColorRGBManager->setColor(switchIndex, r, g, b);

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleRGBPreset()
{
    if (!_fullColorRGBManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"RGB manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("switch") || !doc.containsKey("preset"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing parameters\"}");
        return;
    }

    uint8_t switchIndex = doc["switch"];
    String preset = doc["preset"];

    _fullColorRGBManager->setColorPreset(switchIndex, preset.c_str());

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleRGBBrightness()
{
    if (!_fullColorRGBManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"RGB manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("brightness"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing brightness parameter\"}");
        return;
    }

    uint8_t brightness = doc["brightness"];
    _fullColorRGBManager->setGlobalBrightness(brightness);

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleColorCycleMode()
{
    if (!_colorCycleManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"Color cycle manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("mode"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing mode parameter\"}");
        return;
    }

    uint8_t mode = doc["mode"];
    _colorCycleManager->setCycleMode((ColorCycleManager::CycleMode)mode);

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleColorCycleSpeed()
{
    if (!_colorCycleManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"Color cycle manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("speed"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing speed parameter\"}");
        return;
    }

    uint16_t speed = doc["speed"];
    _colorCycleManager->setCycleSpeed(speed);

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleColorCycleSaturation()
{
    if (!_colorCycleManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"Color cycle manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("saturation"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing saturation parameter\"}");
        return;
    }

    uint8_t saturation = doc["saturation"];
    _colorCycleManager->setCycleSaturation(saturation);

    _server.send(200, "application/json", "{\"success\":true}");
}

void WebServerManager::handleColorCycleMask()
{
    if (!_colorCycleManager)
    {
        _server.send(404, "application/json", "{\"success\":false,\"error\":\"Color cycle manager not available\"}");
        return;
    }

    if (_server.method() != HTTP_POST)
    {
        _server.send(405, "application/json", "{\"success\":false,\"error\":\"Method not allowed\"}");
        return;
    }

    String body = _server.arg("plain");
    DynamicJsonDocument doc(200);

    if (deserializeJson(doc, body) != DeserializationError::Ok)
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid JSON\"}");
        return;
    }

    if (!doc.containsKey("mask"))
    {
        _server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing mask parameter\"}");
        return;
    }

    uint8_t mask = doc["mask"];
    _colorCycleManager->setSwitchMask(mask);

    _server.send(200, "application/json", "{\"success\":true}");
}

#endif // IS_ESP32

#ifdef COMPILE_TEMPERATURE_SENSOR
// Temperature-specific header (adds SSE functionality for real-time temperature updates)
const char WebServerManager::temperatureHeader[] PROGMEM = R"=====(
<style>
.temperature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.temp-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.temp-card:hover {
    transform: translateY(-5px);
}

.temp-value {
    font-size: 3em;
    font-weight: bold;
    margin: 10px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.temp-label {
    font-size: 1.2em;
    opacity: 0.9;
    margin-bottom: 5px;
}

.temp-unit {
    font-size: 0.8em;
    opacity: 0.8;
}

.sensor-status {
    margin-top: 20px;
    padding: 15px;
    border-radius: 10px;
    background: rgba(255,255,255,0.1);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-connected {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-disconnected {
    background-color: #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.last-update {
    font-size: 0.9em;
    opacity: 0.8;
    margin-top: 10px;
}

@media (max-width: 768px) {
    .temperature-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .temp-value {
        font-size: 2.5em;
    }

    .temp-card {
        padding: 20px;
    }
}
</style>
<script>
let temperatureEventSource = null;

function startTemperatureSSE() {
    if (temperatureEventSource) {
        temperatureEventSource.close();
        temperatureEventSource = null;
    }

    temperatureEventSource = new EventSource('/api/temperature/events');

    temperatureEventSource.onopen = function() {
        console.log('Temperature SSE connected');
    };

    temperatureEventSource.addEventListener('connected', function(event) {
        console.log('Temperature SSE ready');
    });

    temperatureEventSource.addEventListener('update', function(event) {
        try {
            const data = JSON.parse(event.data);
            updateTemperatureDisplay(data);
        } catch (e) {
            console.error('Error parsing temperature SSE data:', e);
        }
    });

    temperatureEventSource.onerror = function(event) {
        console.log('Temperature SSE connection error, retrying...');
        setTimeout(() => {
            if (temperatureEventSource) {
                temperatureEventSource.close();
                temperatureEventSource = null;
            }
            startTemperatureSSE();
        }, 3000);
    };
}

function updateTemperatureDisplay(data) {
    // Update temperature value
    const tempElement = document.getElementById('temperature-value');
    if (tempElement) {
        if (data.connected && data.sensorOk) {
            tempElement.textContent = data.temperature.toFixed(1);
        } else {
            tempElement.textContent = '--.-';
        }
    }

    // Update humidity value
    const humElement = document.getElementById('humidity-value');
    if (humElement) {
        if (data.connected && data.sensorOk) {
            humElement.textContent = data.humidity.toFixed(1);
        } else {
            humElement.textContent = '--.-';
        }
    }

    // Update sensor status
    const statusElement = document.getElementById('sensor-status');
    const indicatorElement = document.getElementById('status-indicator');

    if (statusElement && indicatorElement) {
        if (data.connected && data.sensorOk) {
            statusElement.textContent = 'Sensor Connected & Working';
            indicatorElement.className = 'status-indicator status-connected';
        } else if (data.connected && !data.sensorOk) {
            statusElement.textContent = 'Sensor Connected - Read Error';
            indicatorElement.className = 'status-indicator status-disconnected';
        } else {
            statusElement.textContent = 'Sensor Not Responding';
            indicatorElement.className = 'status-indicator status-disconnected';
        }
    }

    // Update last update time
    const updateElement = document.getElementById('last-update');
    if (updateElement) {
        const now = new Date();
        if (data.connected && data.sensorOk) {
            updateElement.textContent = 'Last updated: ' + now.toLocaleTimeString();
        } else {
            updateElement.textContent = 'No data available - ' + now.toLocaleTimeString();
        }
    }
}

// Start SSE when page loads
window.addEventListener('load', function() {
    setActiveNav('/temperature');
    startTemperatureSSE();
});

// Clean up SSE when page unloads
window.addEventListener('beforeunload', function() {
    if (temperatureEventSource) {
        temperatureEventSource.close();
        temperatureEventSource = null;
    }
});
</script>
)=====";

// Temperature page template
const char WebServerManager::temperatureTemplate[] PROGMEM = R"=====(
<div class='content-section'>
<h1>Temperature & Humidity Monitor</h1>

<div class='temperature-grid'>
    <div class='temp-card'>
        <div class='temp-label'>Temperature</div>
        <div class='temp-value' id='temperature-value'>--.-</div>
        <div class='temp-unit'>°C</div>
    </div>

    <div class='temp-card'>
        <div class='temp-label'>Humidity</div>
        <div class='temp-value' id='humidity-value'>--.-</div>
        <div class='temp-unit'>%</div>
    </div>
</div>

<div class='sensor-status'>
    <h3>Sensor Status</h3>
    <div>
        <span class='status-indicator' id='status-indicator'></span>
        <span id='sensor-status'>Connecting...</span>
    </div>
    <div class='last-update' id='last-update'>Waiting for data...</div>
</div>

</div>
)=====";
#endif

// SSE connection management methods implementation
void WebServerManager::updateSSEConnections()
{
    unsigned long currentTime = millis();

    // Update dashboard SSE every 2 seconds
    if (_dashboardSSEActive && (currentTime - _lastDashboardUpdate >= 2000))
    {
        sendDashboardSSEUpdate();
        _lastDashboardUpdate = currentTime;
    }

    // Update temperature SSE every 5 seconds
#ifdef COMPILE_TEMPERATURE_SENSOR
    if (_temperatureSSEActive && (currentTime - _lastTemperatureUpdate >= 5000))
    {
        sendTemperatureSSEUpdate();
        _lastTemperatureUpdate = currentTime;
    }
#endif
}

void WebServerManager::sendDashboardSSEUpdate()
{
    if (!_dashboardSSEActive || !_dashboardSSEClient.connected())
    {
        closeSSEConnection(_dashboardSSEClient, _dashboardSSEActive, "Dashboard");
        return;
    }

    // Build uptime string
    unsigned long uptimeMs = millis();
    unsigned long uptimeSeconds = uptimeMs / 1000;
    unsigned long days = uptimeSeconds / 86400;
    unsigned long hours = (uptimeSeconds % 86400) / 3600;
    unsigned long minutes = (uptimeSeconds % 3600) / 60;
    unsigned long seconds = uptimeSeconds % 60;

    String uptime = "";
    if (days > 0)
        uptime += String(days) + "d ";
    if (hours > 0 || days > 0)
        uptime += String(hours) + "h ";
    if (minutes > 0 || hours > 0 || days > 0)
        uptime += String(minutes) + "m ";
    uptime += String(seconds) + "s";

    // Build update data
    String updateData = "{";
    updateData += "\"freeHeap\":" + String(ESP.getFreeHeap()) + ",";
    updateData += "\"uptime\":\"" + uptime + "\"";
    updateData += "}";

    // Send SSE data
    String sseData = "event: update\r\ndata: " + updateData + "\r\n\r\n";

    if (_dashboardSSEClient.print(sseData) == 0)
    {
        closeSSEConnection(_dashboardSSEClient, _dashboardSSEActive, "Dashboard");
    }
}

void WebServerManager::sendSwitchesSSEUpdate()
{
    if (!_switchesSSEActive || !_switchesSSEClient.connected() || !_deviceManager)
    {
        closeSSEConnection(_switchesSSEClient, _switchesSSEActive, "Switches");
        return;
    }

    // Build complete switch data
    int switchCount = _deviceManager->getSwitchCount();
    String switchData = "{";
    switchData += "\"switchCount\":" + String(switchCount) + ",";

    // Switch states
    switchData += "\"switchState\":[";
    for (int i = 0; i < switchCount; i++)
    {
        if (i > 0)
            switchData += ",";
        switchData += _deviceManager->getSwitchState(i) ? "true" : "false";
    }
    switchData += "],";

    // RGB OFF colors
    switchData += "\"rgbOffColors\":[";
    for (int i = 0; i < switchCount; i++)
    {
        if (i > 0)
            switchData += ",";
        bool r, g, b;
        _deviceManager->getRGBOff(i, r, g, b);
        String colorName = getColorNameFromRGB(r, g, b);
        switchData += "\"" + colorName + "\"";
    }
    switchData += "],";

    // RGB ON colors
    switchData += "\"rgbOnColors\":[";
    for (int i = 0; i < switchCount; i++)
    {
        if (i > 0)
            switchData += ",";
        bool r, g, b;
        _deviceManager->getRGBOn(i, r, g, b);
        String colorName = getColorNameFromRGB(r, g, b);
        switchData += "\"" + colorName + "\"";
    }
    switchData += "]}";

    // Send SSE data
    String sseData = "data: " + switchData + "\r\n\r\n";

    if (_switchesSSEClient.print(sseData) == 0)
    {
        closeSSEConnection(_switchesSSEClient, _switchesSSEActive, "Switches");
    }
}

#ifdef COMPILE_TEMPERATURE_SENSOR
void WebServerManager::sendTemperatureSSEUpdate()
{
    if (!_temperatureSSEActive || !_temperatureSSEClient.connected())
    {
        closeSSEConnection(_temperatureSSEClient, _temperatureSSEActive, "Temperature");
        return;
    }

    // Read current temperature data
    bool sensorOk = false;
    float temperature = 0.0;
    float humidity = 0.0;
    bool connected = false;

    if (_temperatureSensor != nullptr)
    {
        sensorOk = _temperatureSensor->getTemperatureAndHumidity(temperature, humidity);
        connected = _temperatureSensor->isConnected();
    }

    // Build temperature data
    String tempData = "{";
    tempData += "\"temperature\":" + String(sensorOk ? temperature : 0.0, 1) + ",";
    tempData += "\"humidity\":" + String(sensorOk ? humidity : 0.0, 1) + ",";
    tempData += "\"connected\":" + String(connected ? "true" : "false") + ",";
    tempData += "\"sensorOk\":" + String(sensorOk ? "true" : "false") + ",";
    tempData += "\"timestamp\":" + String(millis());
    tempData += "}";

    // Send SSE data
    String sseData = "event: update\r\ndata: " + tempData + "\r\n\r\n";

    if (_temperatureSSEClient.print(sseData) == 0)
    {
        closeSSEConnection(_temperatureSSEClient, _temperatureSSEActive, "Temperature");
    }
}
#endif

void WebServerManager::closeSSEConnection(WiFiClient &client, bool &activeFlag, const String &connectionName)
{
    if (activeFlag)
    {
        Serial.println(connectionName + " SSE connection closed");
        client.stop();
        activeFlag = false;
    }
}

#endif
